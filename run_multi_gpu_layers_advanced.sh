#!/bin/bash

# Advanced Multi-GPU Layer Processing Script
# Distributes layer processing across GPUs with monitoring, logging, and error handling

# Configuration
NUM_GPUS=8
TOTAL_LAYERS=32  # Llama-3.1-8B-Instruct has 32 layers (0-31)
MODEL_NAME="meta-llama/Llama-3.1-8B-Instruct"
SAE_PATH="fnlp/Llama3_1-8B-Base-LXR-32x"
DATA_DIR="/data_x/junkim100/projects/scheming_sae/dataset_creation/scheming_dataset.jsonl"
SAE_FOLDER_NAME="Llama3_1-8B-Base-L16R-32x"

# Logging setup
LOG_DIR="./logs/multi_gpu_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$LOG_DIR"
MAIN_LOG="$LOG_DIR/main.log"

# Arrays to track GPU usage and layer status
declare -a gpu_pids=()
declare -a gpu_layers=()
declare -a completed_layers=()
declare -a failed_layers=()

# Initialize arrays
for ((i=0; i<NUM_GPUS; i++)); do
    gpu_pids[i]=""
    gpu_layers[i]=""
done

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$MAIN_LOG"
}

# Function to check GPU memory usage
check_gpu_memory() {
    local gpu_id=$1
    if command -v nvidia-smi &> /dev/null; then
        local memory_used=$(nvidia-smi --query-gpu=memory.used --format=csv,noheader,nounits -i $gpu_id)
        local memory_total=$(nvidia-smi --query-gpu=memory.total --format=csv,noheader,nounits -i $gpu_id)
        local memory_percent=$((memory_used * 100 / memory_total))
        echo $memory_percent
    else
        echo 0
    fi
}

# Function to find available GPU
find_available_gpu() {
    for ((i=0; i<NUM_GPUS; i++)); do
        if [[ -z "${gpu_pids[i]}" ]] || ! kill -0 "${gpu_pids[i]}" 2>/dev/null; then
            # Check memory usage before assigning
            local mem_usage=$(check_gpu_memory $i)
            if [[ $mem_usage -lt 80 ]]; then  # Only use GPU if <80% memory used
                echo $i
                return
            fi
        fi
    done
    echo -1  # No GPU available
}

# Function to wait for any GPU to become available
wait_for_gpu() {
    while true; do
        gpu_id=$(find_available_gpu)
        if [[ $gpu_id -ne -1 ]]; then
            echo $gpu_id
            return
        fi
        log "All GPUs busy or high memory usage, waiting 30 seconds..."
        print_status
        sleep 30
    done
}

# Function to print current status
print_status() {
    log "=== Current Status ==="
    for ((i=0; i<NUM_GPUS; i++)); do
        if [[ -n "${gpu_pids[i]}" ]] && kill -0 "${gpu_pids[i]}" 2>/dev/null; then
            local mem_usage=$(check_gpu_memory $i)
            log "GPU $i: Processing layer ${gpu_layers[i]} (PID: ${gpu_pids[i]}, Memory: ${mem_usage}%)"
        else
            local mem_usage=$(check_gpu_memory $i)
            log "GPU $i: Available (Memory: ${mem_usage}%)"
        fi
    done
    log "Completed: ${#completed_layers[@]}/${TOTAL_LAYERS} layers"
    log "Failed: ${#failed_layers[@]} layers"
    log "====================="
}

# Function to run layer processing
run_layer() {
    local layer=$1
    local gpu_id=$2
    
    log "Starting layer $layer on GPU $gpu_id"
    gpu_layers[$gpu_id]=$layer
    
    # Create layer-specific log file
    local layer_log="$LOG_DIR/layer_${layer}_gpu_${gpu_id}.log"
    
    # Run the command in background with logging
    CUDA_VISIBLE_DEVICES=$gpu_id python steered_inference.py create_activations \
        --model_name "$MODEL_NAME" \
        --sae_path "$SAE_PATH" \
        --data_dir "$DATA_DIR" \
        --sae_folder_name "$SAE_FOLDER_NAME" \
        --target_layer $layer \
        > "$layer_log" 2>&1 &
    
    local pid=$!
    gpu_pids[$gpu_id]=$pid
    
    log "Layer $layer started on GPU $gpu_id with PID $pid (log: $layer_log)"
    
    # Wait for this process to complete
    wait $pid
    local exit_code=$?
    
    # Clear the GPU slot
    gpu_pids[$gpu_id]=""
    gpu_layers[$gpu_id]=""
    
    if [[ $exit_code -eq 0 ]]; then
        log "Layer $layer completed successfully on GPU $gpu_id"
        completed_layers+=($layer)
    else
        log "Layer $layer failed on GPU $gpu_id with exit code $exit_code"
        failed_layers+=($layer)
    fi
    
    return $exit_code
}

# Signal handlers for graceful shutdown
cleanup() {
    log "Received interrupt signal, cleaning up..."
    for ((i=0; i<NUM_GPUS; i++)); do
        if [[ -n "${gpu_pids[i]}" ]]; then
            log "Terminating process ${gpu_pids[i]} on GPU $i"
            kill -TERM "${gpu_pids[i]}" 2>/dev/null
        fi
    done
    exit 1
}

trap cleanup SIGINT SIGTERM

# Main execution
log "Starting advanced multi-GPU layer processing..."
log "Total layers: $TOTAL_LAYERS"
log "Available GPUs: $NUM_GPUS"
log "Model: $MODEL_NAME"
log "SAE Path: $SAE_PATH"
log "Data Dir: $DATA_DIR"
log "Log Directory: $LOG_DIR"
log ""

# Process all layers
for ((layer=0; layer<TOTAL_LAYERS; layer++)); do
    # Wait for an available GPU
    gpu_id=$(wait_for_gpu)
    
    # Run layer processing in background
    run_layer $layer $gpu_id &
    
    # Small delay to avoid race conditions
    sleep 5
    
    # Print status every 4 layers
    if [[ $((layer % 4)) -eq 0 ]]; then
        print_status
    fi
done

# Wait for all remaining processes to complete
log "Waiting for all remaining processes to complete..."
while true; do
    active_processes=0
    for ((i=0; i<NUM_GPUS; i++)); do
        if [[ -n "${gpu_pids[i]}" ]] && kill -0 "${gpu_pids[i]}" 2>/dev/null; then
            active_processes=$((active_processes + 1))
        fi
    done
    
    if [[ $active_processes -eq 0 ]]; then
        break
    fi
    
    log "Still waiting for $active_processes processes..."
    print_status
    sleep 30
done

# Final summary
log ""
log "=== Final Summary ==="
log "Total layers processed: $TOTAL_LAYERS"
log "Successfully completed: ${#completed_layers[@]}"
log "Failed: ${#failed_layers[@]}"

if [[ ${#failed_layers[@]} -gt 0 ]]; then
    log "Failed layers: ${failed_layers[*]}"
    log "Check individual layer logs in $LOG_DIR for details"
fi

log "All layer processing completed!"
log "Logs saved in: $LOG_DIR"
