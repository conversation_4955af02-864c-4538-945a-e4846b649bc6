#!/bin/bash

# Multi-GPU Layer Processing Script
# Runs steered_inference.py create_activations for all layers across 8 GPUs

# Configuration
NUM_GPUS=8
TOTAL_LAYERS=32
MODEL_NAME="meta-llama/Llama-3.1-8B-Instruct"
SAE_PATH="fnlp/Llama3_1-8B-Base-LXR-32x"
DATA_DIR="/data_x/junkim100/projects/scheming_sae/dataset_creation/scheming_dataset_llama-3.1-8B-Instruct.jsonl"
RESULTS_DIR="/data_x/junkim100/projects/scheming_sae/itas/results/"

# Arrays to track GPU usage
declare -a gpu_pids=()
for ((i=0; i<NUM_GPUS; i++)); do
    gpu_pids[i]=""
done

# Function to find available GPU
find_available_gpu() {
    for ((i=0; i<NUM_GPUS; i++)); do
        if [[ -z "${gpu_pids[i]}" ]] || ! kill -0 "${gpu_pids[i]}" 2>/dev/null; then
            echo $i
            return
        fi
    done
    echo -1
}

# Function to wait for available GPU
wait_for_gpu() {
    while true; do
        gpu_id=$(find_available_gpu)
        if [[ $gpu_id -ne -1 ]]; then
            echo $gpu_id
            return
        fi
        echo "All GPUs busy, waiting 30 seconds..."
        sleep 30
    done
}

# Function to run layer processing
run_layer() {
    local layer=$1
    local gpu_id=$2

    echo "Starting layer $layer on GPU $gpu_id"

    CUDA_VISIBLE_DEVICES=$gpu_id python steered_inference.py create_activations \
        --model_name "$MODEL_NAME" \
        --sae_path "$SAE_PATH" \
        --data_source "$DATA_DIR" \
        --target_layer $layer \
        --results_dir "$RESULTS_DIR" &

    local pid=$!
    gpu_pids[$gpu_id]=$pid

    echo "Layer $layer started on GPU $gpu_id with PID $pid"

    wait $pid
    local exit_code=$?

    gpu_pids[$gpu_id]=""

    if [[ $exit_code -eq 0 ]]; then
        echo "Layer $layer completed successfully on GPU $gpu_id"
    else
        echo "Layer $layer failed on GPU $gpu_id with exit code $exit_code"
    fi

    return $exit_code
}

# Main execution
echo "Starting multi-GPU layer processing..."
echo "Total layers: $TOTAL_LAYERS"
echo "Available GPUs: $NUM_GPUS"
echo "Model: $MODEL_NAME"
echo "SAE Path: $SAE_PATH"
echo "Data Dir: $DATA_DIR"
echo "Results Dir: $RESULTS_DIR"
echo ""

# Create results directory
mkdir -p "$RESULTS_DIR"

# Process all layers
for ((layer=0; layer<TOTAL_LAYERS; layer++)); do
    gpu_id=$(wait_for_gpu)
    run_layer $layer $gpu_id &
    sleep 2
done

# Wait for all remaining processes
echo "Waiting for all remaining processes to complete..."
for ((i=0; i<NUM_GPUS; i++)); do
    if [[ -n "${gpu_pids[i]}" ]]; then
        wait "${gpu_pids[i]}"
        echo "GPU $i process completed"
    fi
done

echo "All layer processing completed!"
echo "Results saved in: $RESULTS_DIR"
