#!/bin/bash

# Multi-GPU Layer Processing Script
# Runs steered_inference.py create_activations for all layers across 8 GPUs

# Configuration
AVAILABLE_GPUS=(0 1 2 3 4 5 6 7)  # Specify which GPUs to use
TOTAL_LAYERS=32
MODEL_NAME="meta-llama/Llama-3.1-8B-Instruct"
SAE_PATH="fnlp/Llama3_1-8B-Base-LXR-32x"
DATA_DIR="/data_x/junkim100/projects/scheming_sae/dataset_creation/scheming_dataset_llama-3.1-8B-Instruct.jsonl"
RESULTS_DIR="/data_x/junkim100/projects/scheming_sae/itas/results/"

NUM_GPUS=${#AVAILABLE_GPUS[@]}

# Arrays to track GPU usage (indexed by GPU slot, not GPU ID)
declare -a gpu_pids=()
declare -a gpu_layers=()
for ((i=0; i<NUM_GPUS; i++)); do
    gpu_pids[i]=""
    gpu_layers[i]=""
done

# Function to find available GPU slot
find_available_gpu_slot() {
    for ((i=0; i<NUM_GPUS; i++)); do
        if [[ -z "${gpu_pids[i]}" ]] || ! kill -0 "${gpu_pids[i]}" 2>/dev/null; then
            echo $i
            return
        fi
    done
    echo -1
}

# Function to wait for available GPU slot
wait_for_gpu_slot() {
    while true; do
        slot=$(find_available_gpu_slot)
        if [[ $slot -ne -1 ]]; then
            echo $slot
            return
        fi
        echo "All GPUs busy, waiting 30 seconds..."
        print_gpu_status
        sleep 30
    done
}

# Function to print GPU status
print_gpu_status() {
    echo "=== GPU Status ==="
    for ((i=0; i<NUM_GPUS; i++)); do
        gpu_id=${AVAILABLE_GPUS[i]}
        if [[ -n "${gpu_pids[i]}" ]] && kill -0 "${gpu_pids[i]}" 2>/dev/null; then
            echo "GPU $gpu_id (slot $i): Processing layer ${gpu_layers[i]} (PID: ${gpu_pids[i]})"
        else
            echo "GPU $gpu_id (slot $i): Available"
        fi
    done
    echo "=================="
}

# Function to run layer processing
run_layer() {
    local layer=$1
    local slot=$2
    local gpu_id=${AVAILABLE_GPUS[slot]}

    echo "Starting layer $layer on GPU $gpu_id (slot $slot)"
    gpu_layers[slot]=$layer

    CUDA_VISIBLE_DEVICES=$gpu_id python steered_inference.py create_activations \
        --model_name "$MODEL_NAME" \
        --sae_path "$SAE_PATH" \
        --data_source "$DATA_DIR" \
        --target_layer $layer &

    local pid=$!
    gpu_pids[slot]=$pid

    echo "Layer $layer started on GPU $gpu_id (slot $slot) with PID $pid"
}

# Function to wait for a specific process to complete
wait_for_layer() {
    local slot=$1
    local layer=${gpu_layers[slot]}
    local gpu_id=${AVAILABLE_GPUS[slot]}
    local pid=${gpu_pids[slot]}

    wait $pid
    local exit_code=$?

    # Clear the slot
    gpu_pids[slot]=""
    gpu_layers[slot]=""

    if [[ $exit_code -eq 0 ]]; then
        echo "Layer $layer completed successfully on GPU $gpu_id (slot $slot)"
    else
        echo "Layer $layer failed on GPU $gpu_id (slot $slot) with exit code $exit_code"
    fi

    return $exit_code
}

# Main execution
echo "Starting multi-GPU layer processing..."
echo "Total layers: $TOTAL_LAYERS"
echo "Available GPUs: ${AVAILABLE_GPUS[*]} (${NUM_GPUS} total)"
echo "Model: $MODEL_NAME"
echo "SAE Path: $SAE_PATH"
echo "Data Dir: $DATA_DIR"
echo "Results Dir: $RESULTS_DIR"
echo ""

# Create results directory
mkdir -p "$RESULTS_DIR"

# Process all layers with proper GPU management
layer=0
while [[ $layer -lt $TOTAL_LAYERS ]]; do
    # Find available GPU slot
    slot=$(wait_for_gpu_slot)

    # Start layer processing on the available slot
    run_layer $layer $slot

    echo "Layer $layer assigned to GPU ${AVAILABLE_GPUS[slot]} (slot $slot)"
    layer=$((layer + 1))

    # Small delay to avoid race conditions
    sleep 2

    # Check if any processes have completed and clean up
    for ((i=0; i<NUM_GPUS; i++)); do
        if [[ -n "${gpu_pids[i]}" ]] && ! kill -0 "${gpu_pids[i]}" 2>/dev/null; then
            # Process completed, clean up
            gpu_pids[i]=""
            gpu_layers[i]=""
        fi
    done

    # Print status every 4 layers
    if [[ $((layer % 4)) -eq 0 ]]; then
        print_gpu_status
    fi
done

# Wait for all remaining processes to complete
echo ""
echo "All layers assigned. Waiting for remaining processes to complete..."
print_gpu_status

for ((i=0; i<NUM_GPUS; i++)); do
    if [[ -n "${gpu_pids[i]}" ]]; then
        echo "Waiting for layer ${gpu_layers[i]} on GPU ${AVAILABLE_GPUS[i]} (slot $i)..."
        wait_for_layer $i
    fi
done

echo ""
echo "All layer processing completed!"
echo "Results saved in: $RESULTS_DIR"
