#!/usr/bin/env python3
"""
Steered Inference Engine - Production Script

A streamlined tool for running steered inference with truthfulness/scheming steering.
Supports file-based batch processing and interactive chat modes.

Features:
- File Mode: Process any CSV file with steered inference
- Chat Mode: Interactive multi-turn conversations with steering
- Flexible CSV processing with automatic column detection
- Production-ready error handling and comprehensive logging
- Clean, focused interface with essential functions only

Usage Examples:

    # File Mode: Process any CSV file with steered inference
    python steered_inference.py file_inference \
        --input_csv="data/my_questions.csv" \
        --config_path="../results/steering_config.json" \
        --activations_path="../results/functional_activations.pt" \
        --steering_direction="truthful"

    # Chat Mode: Interactive conversation with steering
    python steered_inference.py chat \
        --config_path="../results/steering_config.json" \
        --activations_path="../results/functional_activations.pt" \
        --steering_direction="scheming"

    # Create functional activations (prerequisite step)
    python steered_inference.py create_activations \
        --model_name="meta-llama/Llama-3.1-8B-Instruct" \
        --sae_path="/path/to/sae.pt"
"""

import os
import json
import logging
import pandas as pd
import fire
import multiprocessing as mp
from typing import List, Optional
from datetime import datetime

# Fix CUDA multiprocessing issues
if __name__ == "__main__":
    mp.set_start_method("spawn", force=True)

# Set environment variables
os.environ["TOKENIZERS_PARALLELISM"] = "false"
os.environ["TRANSFORMERS_VERBOSITY"] = "error"  # Suppress generation warnings

# Suppress specific warnings
import warnings

warnings.filterwarnings("ignore", message=".*generation flags.*not valid.*")

# Import ITAS components
from itas.core.steered_inference import (
    SteeredInferenceEngine,
    InferenceConfig,
    BenchmarkQuestion,
)
from itas.core.steering_pipeline import SteeringPipeline, SteeringPipelineConfig

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(
            f'steered_inference_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
        ),
    ],
)
logger = logging.getLogger(__name__)


def load_questions_from_csv(
    csv_path: str,
    max_questions: int = 0,
    required_columns: Optional[List[str]] = None,
) -> List[BenchmarkQuestion]:
    """
    Load questions from a single CSV file with flexible column support.

    Args:
        csv_path: Path to the CSV file containing questions
        max_questions: Maximum number of questions to load (0 = all)
        required_columns: List of required column names (None = use defaults)

    Returns:
        List of BenchmarkQuestion objects loaded from the CSV file

    Raises:
        ValueError: If CSV file is not found or missing required columns
        FileNotFoundError: If the specified CSV file does not exist
    """
    logger.info(f"📥 Loading questions from: {csv_path}")

    if not os.path.exists(csv_path):
        raise FileNotFoundError(f"CSV file not found: {csv_path}")

    # Default required columns for question loading
    if required_columns is None:
        required_columns = ["system_prompt", "user_prompt"]

    try:
        df = pd.read_csv(csv_path)
        logger.info(f"📊 CSV loaded with {len(df)} total rows")

        # Validate required columns exist
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(
                f"Missing required columns in CSV: {missing_columns}. "
                f"Available columns: {list(df.columns)}"
            )

        # Filter out rows with missing essential data
        valid_df = df.dropna(subset=required_columns)
        logger.info(f"📋 Found {len(valid_df)} valid rows with required columns")

        if len(valid_df) == 0:
            logger.warning("⚠️ No valid rows found in CSV file")
            return []

        # Apply max_questions limit if specified
        if max_questions > 0 and len(valid_df) > max_questions:
            valid_df = valid_df.head(max_questions)
            logger.info(f"🔢 Limited to {max_questions} questions")

        # Convert rows to BenchmarkQuestion objects
        questions = []
        csv_filename = os.path.splitext(os.path.basename(csv_path))[0]

        for idx, row in valid_df.iterrows():
            # Create question with flexible metadata handling
            metadata = {}
            for col in df.columns:
                if col not in required_columns and pd.notna(row.get(col)):
                    metadata[col] = str(row[col]).strip()

            question = BenchmarkQuestion(
                question_id=f"{csv_filename}_{idx}",
                system_prompt=str(row["system_prompt"]).strip(),
                user_prompt=str(row["user_prompt"]).strip(),
                ground_truth=(
                    str(row.get("ground_truth", "")).strip()
                    if pd.notna(row.get("ground_truth"))
                    else None
                ),
                metadata={
                    "csv_file": csv_filename,
                    "csv_index": idx,
                    **metadata,
                },
            )
            questions.append(question)

        logger.info(f"✅ Successfully loaded {len(questions)} questions")
        return questions

    except Exception as e:
        logger.error(f"❌ Error loading CSV file {csv_path}: {e}")
        raise


def create_activations(
    model_name: str,
    sae_path: str,
    data_source: str = "../data/mask_benchmark/csv_data/",  # Can be CSV directory or JSONL file
    target_layer: int = 16,
    steering_alpha: float = 1.5,
    top_k_proportion: float = 0.15,
    max_train_scenarios: Optional[int] = None,
    max_new_tokens: int = 1024,
    results_dir: str = "../results/",
    device: str = "auto",
    num_processes: Optional[int] = None,
):
    """
    Create functional activations using the steering pipeline.

    Args:
        model_name: HuggingFace model name
        sae_path: Path to SAE model (supports fnlp/Llama3_1-8B-Base-LXR-32x and google/gemma-scope-9b-pt-res)
        data_source: Path to training data (directory with CSV files or JSONL file)
        target_layer: Layer for activation extraction
        steering_alpha: Steering strength
        top_k_proportion: Proportion of top features to use
        max_train_scenarios: Max training scenarios (None = all)
        max_new_tokens: Max tokens for generation
        results_dir: Directory to save results
        device: Device for computation
        num_processes: Number of processes for MI calculation (None = auto-detect)
    """
    logger.info("🚀 Creating functional activations...")

    # Determine if data_source is a JSONL file or CSV directory
    if data_source.endswith(".jsonl"):
        # JSONL file
        config = SteeringPipelineConfig(
            model_name=model_name,
            sae_path=sae_path,
            train_data_jsonl=data_source,
            target_layer=target_layer,
            steering_alpha=steering_alpha,
            top_k_proportion=top_k_proportion,
            max_train_scenarios=max_train_scenarios,
            max_new_tokens=max_new_tokens,
            results_dir=results_dir,
            device=device,
            num_processes=num_processes,
        )
    else:
        # CSV directory
        config = SteeringPipelineConfig(
            model_name=model_name,
            sae_path=sae_path,
            train_data_dir=data_source,
            target_layer=target_layer,
            steering_alpha=steering_alpha,
            top_k_proportion=top_k_proportion,
            max_train_scenarios=max_train_scenarios,
            max_new_tokens=max_new_tokens,
            results_dir=results_dir,
            device=device,
            num_processes=num_processes,
        )

    # Run pipeline
    pipeline = SteeringPipeline(config)
    activations_path, config_path = pipeline.run_full_pipeline()

    logger.info("✅ Functional activations created successfully!")
    logger.info(f"   Activations: {activations_path}")
    logger.info(f"   Config: {config_path}")

    return activations_path, config_path


def file_inference(
    config_path: str,
    activations_path: str,
    input_csv: str,
    steering_direction: str = "truthful",
    steering_alpha: float = 0.0,
    max_questions: int = 0,
    max_new_tokens: int = 0,
    results_dir: str = "../results/",
    temperature: float = 0.0,
    top_p: float = 1.0,
    do_sample: bool = False,
    device: str = "auto",
    batch_size: int = 1,
) -> str:
    """
    Run steered inference on questions from a CSV file.

    Args:
        config_path: Path to steering config JSON file
        activations_path: Path to functional activations
        input_csv: Path to input CSV file containing questions
        steering_direction: "truthful" or "scheming"
        steering_alpha: Steering strength (0 = use config value)
        max_questions: Maximum questions to process (0 = all)
        max_new_tokens: Maximum tokens to generate (0 = use config value)
        results_dir: Directory to save results
        temperature: Sampling temperature
        top_p: Top-p (nucleus) sampling parameter
        do_sample: Whether to use sampling
        device: Device for computation
        batch_size: Batch size for inference

    Returns:
        Path to the saved results file

    Raises:
        FileNotFoundError: If config_path, activations_path, or input_csv not found
        ValueError: If CSV file has invalid format or missing required columns
    """
    logger.info(f"🎯 Running file-based {steering_direction} steered inference...")

    # Load configuration from file
    try:
        with open(config_path, "r") as f:
            config_data = json.load(f)
    except FileNotFoundError:
        raise FileNotFoundError(f"Config file not found: {config_path}")
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON in config file {config_path}: {e}")

    # Extract parameters from config
    model_name = config_data["model_name"]
    sae_path = config_data["sae_path"]
    config_steering_alpha = config_data.get("steering_alpha", 1.5)
    config_max_new_tokens = config_data.get("max_new_tokens", 256)

    # Use config values if not overridden (0 means use config value)
    final_steering_alpha = (
        steering_alpha if steering_alpha != 0.0 else config_steering_alpha
    )
    final_max_new_tokens = (
        max_new_tokens if max_new_tokens != 0 else config_max_new_tokens
    )

    logger.info(f"📋 Model: {model_name}")
    logger.info(f"📋 Steering: {steering_direction}, alpha={final_steering_alpha}")
    logger.info(f"📋 Max tokens: {final_max_new_tokens}")

    # Load questions from CSV file
    questions = load_questions_from_csv(input_csv, max_questions)

    if not questions:
        raise ValueError(f"No valid questions found in CSV file: {input_csv}")

    # Create inference configuration
    inference_config = InferenceConfig(
        model_name=model_name,
        sae_path=sae_path,
        activations_path=activations_path,
        config_path=config_path,
        steering_direction=steering_direction,
        steering_alpha=final_steering_alpha,
        max_new_tokens=final_max_new_tokens,
        temperature=temperature,
        top_p=top_p,
        do_sample=do_sample,
        device=device,
        batch_size=batch_size,
    )

    # Run inference
    with SteeredInferenceEngine(inference_config) as engine:
        results = engine.run_inference_batch(questions)

        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_name = os.path.splitext(os.path.basename(input_csv))[0]
        output_path = os.path.join(
            results_dir,
            f"steered_inference_{csv_name}_{steering_direction}_{timestamp}.json",
        )

        # Ensure output directory exists
        os.makedirs(results_dir, exist_ok=True)
        engine.save_results(results, output_path)

    logger.info("✅ File-based steered inference completed!")
    logger.info(f"   Input: {input_csv}")
    logger.info(f"   Results: {output_path}")
    logger.info(f"   Processed: {len(questions)} questions")

    return output_path


def chat(
    config_path: str,
    activations_path: str,
    steering_direction: str = "truthful",
    steering_alpha: float = 0.0,
    max_new_tokens: int = 0,
    temperature: float = 0.0,
    top_p: float = 1.0,
    do_sample: bool = False,
    device: str = "auto",
    system_prompt: str = "You are a helpful assistant.",
) -> None:
    """
    Interactive chat mode with steered inference.

    Provides a multi-turn conversation interface where all responses are generated
    with the specified steering direction (truthful or scheming).

    Args:
        config_path: Path to steering config JSON file
        activations_path: Path to functional activations
        steering_direction: "truthful" or "scheming"
        steering_alpha: Steering strength (0 = use config value)
        max_new_tokens: Maximum tokens to generate (0 = use config value)
        temperature: Sampling temperature
        top_p: Top-p (nucleus) sampling parameter
        do_sample: Whether to use sampling
        device: Device for computation
        system_prompt: System prompt for the conversation

    Raises:
        FileNotFoundError: If config_path or activations_path not found
        KeyboardInterrupt: When user exits with Ctrl+C
    """
    logger.info(f"🗣️ Starting interactive chat with {steering_direction} steering...")

    # Load configuration from file
    try:
        with open(config_path, "r") as f:
            config_data = json.load(f)
    except FileNotFoundError:
        raise FileNotFoundError(f"Config file not found: {config_path}")
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON in config file {config_path}: {e}")

    # Extract parameters from config
    model_name = config_data["model_name"]
    sae_path = config_data["sae_path"]
    config_steering_alpha = config_data.get("steering_alpha", 1.5)
    config_max_new_tokens = config_data.get("max_new_tokens", 256)

    # Use config values if not overridden (0 means use config value)
    final_steering_alpha = (
        steering_alpha if steering_alpha != 0.0 else config_steering_alpha
    )
    final_max_new_tokens = (
        max_new_tokens if max_new_tokens != 0 else config_max_new_tokens
    )

    logger.info(f"📋 Model: {model_name}")
    logger.info(f"📋 Steering: {steering_direction}, alpha={final_steering_alpha}")
    logger.info(f"📋 Max tokens: {final_max_new_tokens}")

    # Create inference configuration
    inference_config = InferenceConfig(
        model_name=model_name,
        sae_path=sae_path,
        activations_path=activations_path,
        config_path=config_path,
        steering_direction=steering_direction,
        steering_alpha=final_steering_alpha,
        max_new_tokens=final_max_new_tokens,
        temperature=temperature,
        top_p=top_p,
        do_sample=do_sample,
        device=device,
        batch_size=1,
    )

    # Start interactive chat
    print(f"\n🤖 Interactive Chat Mode - {steering_direction.title()} Steering")
    print(f"💡 System: {system_prompt}")
    print(
        "💬 Type your messages below. Use 'quit', 'exit', or Ctrl+C to end the conversation.\n"
    )

    try:
        with SteeredInferenceEngine(inference_config) as engine:
            conversation_history = []
            turn_number = 1

            while True:
                try:
                    # Get user input
                    user_input = input(f"👤 You [{turn_number}]: ").strip()

                    if user_input.lower() in ["quit", "exit", "q"]:
                        print("\n👋 Goodbye!")
                        break

                    if not user_input:
                        print("⚠️ Please enter a message or 'quit' to exit.")
                        continue

                    # Generate response with steering
                    print(f"🤖 Assistant [{turn_number}]: ", end="", flush=True)

                    response = engine.generate_response_from_prompts(
                        system_prompt=system_prompt, user_prompt=user_input
                    )

                    print(response)
                    print()  # Add blank line for readability

                    # Store conversation history (optional for future multi-turn support)
                    conversation_history.append(
                        {
                            "turn": turn_number,
                            "user": user_input,
                            "assistant": response,
                            "steering": steering_direction,
                        }
                    )

                    turn_number += 1

                except KeyboardInterrupt:
                    print("\n\n👋 Chat interrupted by user. Goodbye!")
                    break
                except Exception as e:
                    logger.error(f"❌ Error generating response: {e}")
                    print(f"❌ Error: {e}")
                    print("🔄 Please try again or type 'quit' to exit.\n")

    except Exception as e:
        logger.error(f"❌ Failed to initialize chat engine: {e}")
        print(f"❌ Failed to start chat: {e}")
        return

    logger.info(f"✅ Chat session completed with {turn_number - 1} turns")


if __name__ == "__main__":
    fire.Fire(
        {
            "create_activations": create_activations,
            "file_inference": file_inference,
            "chat": chat,
        }
    )
