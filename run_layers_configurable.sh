#!/bin/bash

# Configurable Multi-GPU Layer Processing Script
# Uses gpu_config.sh for configuration

# Load configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/gpu_config.sh"

# Logging setup
LOG_DIR="$LOG_BASE_DIR/multi_gpu_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$LOG_DIR"
MAIN_LOG="$LOG_DIR/main.log"

# Arrays to track GPU usage and layer status
declare -a gpu_pids=()
declare -a gpu_layers=()
declare -a completed_layers=()
declare -a failed_layers=()

# Initialize arrays
for ((i=0; i<NUM_GPUS; i++)); do
    gpu_pids[i]=""
    gpu_layers[i]=""
done

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$MAIN_LOG"
}

# Function to check GPU memory usage
check_gpu_memory() {
    local gpu_id=$1
    if command -v nvidia-smi &> /dev/null; then
        local memory_used=$(nvidia-smi --query-gpu=memory.used --format=csv,noheader,nounits -i $gpu_id 2>/dev/null || echo "0")
        local memory_total=$(nvidia-smi --query-gpu=memory.total --format=csv,noheader,nounits -i $gpu_id 2>/dev/null || echo "1")
        local memory_percent=$((memory_used * 100 / memory_total))
        echo $memory_percent
    else
        echo 0
    fi
}

# Function to find available GPU
find_available_gpu() {
    for ((i=0; i<NUM_GPUS; i++)); do
        if [[ -z "${gpu_pids[i]}" ]] || ! kill -0 "${gpu_pids[i]}" 2>/dev/null; then
            # Check memory usage before assigning
            local mem_usage=$(check_gpu_memory $i)
            if [[ $mem_usage -lt $MEMORY_THRESHOLD ]]; then
                echo $i
                return
            fi
        fi
    done
    echo -1  # No GPU available
}

# Function to wait for any GPU to become available
wait_for_gpu() {
    while true; do
        gpu_id=$(find_available_gpu)
        if [[ $gpu_id -ne -1 ]]; then
            echo $gpu_id
            return
        fi
        log "All GPUs busy or high memory usage, waiting $WAIT_TIME seconds..."
        print_status
        sleep $WAIT_TIME
    done
}

# Function to print current status
print_status() {
    log "=== Current Status ==="
    for ((i=0; i<NUM_GPUS; i++)); do
        if [[ -n "${gpu_pids[i]}" ]] && kill -0 "${gpu_pids[i]}" 2>/dev/null; then
            local mem_usage=$(check_gpu_memory $i)
            log "GPU $i: Processing layer ${gpu_layers[i]} (PID: ${gpu_pids[i]}, Memory: ${mem_usage}%)"
        else
            local mem_usage=$(check_gpu_memory $i)
            log "GPU $i: Available (Memory: ${mem_usage}%)"
        fi
    done
    log "Completed: ${#completed_layers[@]}/${TOTAL_LAYERS} layers"
    log "Failed: ${#failed_layers[@]} layers"
    log "====================="
}

# Function to run layer processing
run_layer() {
    local layer=$1
    local gpu_id=$2
    
    log "Starting layer $layer on GPU $gpu_id"
    gpu_layers[$gpu_id]=$layer
    
    # Create layer-specific log file
    local layer_log="$LOG_DIR/layer_${layer}_gpu_${gpu_id}.log"
    
    # Run the command in background with logging
    CUDA_VISIBLE_DEVICES=$gpu_id python "$PYTHON_SCRIPT" create_activations \
        --model_name "$MODEL_NAME" \
        --sae_path "$SAE_PATH" \
        --data_dir "$DATA_DIR" \
        --sae_folder_name "$SAE_FOLDER_NAME" \
        --target_layer $layer \
        > "$layer_log" 2>&1 &
    
    local pid=$!
    gpu_pids[$gpu_id]=$pid
    
    log "Layer $layer started on GPU $gpu_id with PID $pid (log: $layer_log)"
    
    # Wait for this process to complete
    wait $pid
    local exit_code=$?
    
    # Clear the GPU slot
    gpu_pids[$gpu_id]=""
    gpu_layers[$gpu_id]=""
    
    if [[ $exit_code -eq 0 ]]; then
        log "Layer $layer completed successfully on GPU $gpu_id"
        completed_layers+=($layer)
    else
        log "Layer $layer failed on GPU $gpu_id with exit code $exit_code"
        failed_layers+=($layer)
    fi
    
    return $exit_code
}

# Signal handlers for graceful shutdown
cleanup() {
    log "Received interrupt signal, cleaning up..."
    for ((i=0; i<NUM_GPUS; i++)); do
        if [[ -n "${gpu_pids[i]}" ]]; then
            log "Terminating process ${gpu_pids[i]} on GPU $i"
            kill -TERM "${gpu_pids[i]}" 2>/dev/null
        fi
    done
    exit 1
}

trap cleanup SIGINT SIGTERM

# Main execution
log "Starting configurable multi-GPU layer processing..."
log "Configuration loaded from: $SCRIPT_DIR/gpu_config.sh"
log "Total layers: $TOTAL_LAYERS"
log "Available GPUs: $NUM_GPUS"
log "Model: $MODEL_NAME"
log "SAE Path: $SAE_PATH"
log "Data Dir: $DATA_DIR"
log "Memory Threshold: $MEMORY_THRESHOLD%"
log "Log Directory: $LOG_DIR"
log ""

# Validate that the Python script exists
if [[ ! -f "$PYTHON_SCRIPT" ]]; then
    log "ERROR: Python script '$PYTHON_SCRIPT' not found in current directory"
    log "Please ensure you're running this script from the correct directory"
    exit 1
fi

# Process all layers
for ((layer=0; layer<TOTAL_LAYERS; layer++)); do
    # Wait for an available GPU
    gpu_id=$(wait_for_gpu)
    
    # Run layer processing in background
    run_layer $layer $gpu_id &
    
    # Small delay to avoid race conditions
    sleep 5
    
    # Print status at intervals
    if [[ $((layer % STATUS_INTERVAL)) -eq 0 ]]; then
        print_status
    fi
done

# Wait for all remaining processes to complete
log "Waiting for all remaining processes to complete..."
while true; do
    active_processes=0
    for ((i=0; i<NUM_GPUS; i++)); do
        if [[ -n "${gpu_pids[i]}" ]] && kill -0 "${gpu_pids[i]}" 2>/dev/null; then
            active_processes=$((active_processes + 1))
        fi
    done
    
    if [[ $active_processes -eq 0 ]]; then
        break
    fi
    
    log "Still waiting for $active_processes processes..."
    print_status
    sleep $WAIT_TIME
done

# Final summary
log ""
log "=== Final Summary ==="
log "Total layers processed: $TOTAL_LAYERS"
log "Successfully completed: ${#completed_layers[@]}"
log "Failed: ${#failed_layers[@]}"

if [[ ${#failed_layers[@]} -gt 0 ]]; then
    log "Failed layers: ${failed_layers[*]}"
    log "Check individual layer logs in $LOG_DIR for details"
    
    # Create retry script for failed layers
    retry_script="$LOG_DIR/retry_failed_layers.sh"
    echo "#!/bin/bash" > "$retry_script"
    echo "# Retry script for failed layers" >> "$retry_script"
    echo "source \"$SCRIPT_DIR/gpu_config.sh\"" >> "$retry_script"
    echo "" >> "$retry_script"
    for layer in "${failed_layers[@]}"; do
        echo "echo \"Retrying layer $layer...\"" >> "$retry_script"
        echo "CUDA_VISIBLE_DEVICES=0 python \"$PYTHON_SCRIPT\" create_activations \\" >> "$retry_script"
        echo "    --model_name \"$MODEL_NAME\" \\" >> "$retry_script"
        echo "    --sae_path \"$SAE_PATH\" \\" >> "$retry_script"
        echo "    --data_dir \"$DATA_DIR\" \\" >> "$retry_script"
        echo "    --sae_folder_name \"$SAE_FOLDER_NAME\" \\" >> "$retry_script"
        echo "    --target_layer $layer" >> "$retry_script"
        echo "" >> "$retry_script"
    done
    chmod +x "$retry_script"
    log "Retry script created: $retry_script"
fi

log "All layer processing completed!"
log "Logs saved in: $LOG_DIR"
