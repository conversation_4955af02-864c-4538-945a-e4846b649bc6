#!/bin/bash

# GPU Configuration File
# Modify these variables according to your setup

# Hardware Configuration
export NUM_GPUS=8
export TOTAL_LAYERS=32  # Llama-3.1-8B-Instruct has 32 layers (0-31)

# Model Configuration
export MODEL_NAME="meta-llama/Llama-3.1-8B-Instruct"
export SAE_PATH="fnlp/Llama3_1-8B-Base-LXR-32x"
export DATA_DIR="/data_x/junkim100/projects/scheming_sae/dataset_creation/scheming_dataset.jsonl"
export SAE_FOLDER_NAME="Llama3_1-8B-Base-L16R-32x"

# Processing Configuration
export MEMORY_THRESHOLD=80  # Don't use GPU if memory usage > this percentage
export WAIT_TIME=30         # Seconds to wait when all GPUs are busy
export STATUS_INTERVAL=4    # Print status every N layers

# Script paths
export SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
export PYTHON_SCRIPT="steered_inference.py"

# Logging
export LOG_BASE_DIR="./logs"

echo "GPU Configuration loaded:"
echo "  GPUs: $NUM_GPUS"
echo "  Layers: $TOTAL_LAYERS"
echo "  Model: $MODEL_NAME"
echo "  SAE Path: $SAE_PATH"
echo "  Data Dir: $DATA_DIR"
